# ZTE 知识库建设技术选型方案

## 一、项目现状分析

### 1.1 现有技术栈
- **后端框架**: Python + Flask
- **向量数据库**: Milvus 2.4.1
- **全文检索**: Elasticsearch
- **知识图谱**: 自建KG系统
- **向量模型**: BGE-M3
- **配置中心**: Apollo
- **监控**: Apache SkyWalking

### 1.2 已实现功能
- RAG (Retrieval-Augmented Generation) 架构
- 多路召回机制（ES + Milvus + KG）
- BGE-M3 重排序
- 流式LLM响应
- 中英文双语支持

### 1.3 存在的问题
- 缺少文档解析能力（Word、Excel、PDF等）
- 串行处理影响性能
- 代码重复率高
- 缺少缓存机制
- 监控和日志不完善

## 二、技术选型方案

### 2.1 文档解析技术选型

| 文档类型 | 推荐方案 | 备选方案 | 选择理由 |
|---------|---------|---------|----------|
| **PDF** | pdfplumber | PyPDF2 | 表格和图片处理能力强，OCR支持好 |
| **Word** | python-docx | mammoth | 与Python生态兼容，处理docx格式稳定 |
| **Excel** | pandas + openpyxl | xlrd | pandas已在项目依赖中，减少新增依赖 |
| **PPT** | python-pptx | - | 原生Python支持，解析效果好 |
| **通用** | Apache Tika | textract | 支持1500+格式，企业级稳定 |

### 2.2 向量化技术升级

#### 短期方案（1-2个月）
- 保持BGE-M3模型不变
- 本地部署模型服务，减少网络延迟
- 添加向量缓存机制

#### 中期方案（3-6个月）
- 升级到BGE-M3-v2或BGE-reranker-v2-m3
- 引入Jina-embeddings-v3作为备选
- 实现多模型融合策略

#### 长期方案（6个月+）
- 引入多模态向量模型（如CLIP）
- 支持图文混合检索
- 自研领域特定向量模型

### 2.3 存储架构优化

```
优化后的存储架构：
├── Milvus（向量存储）
│   ├── document_embeddings    # 文档级向量
│   ├── paragraph_embeddings   # 段落级向量
│   ├── sentence_embeddings    # 句子级向量（新增）
│   └── image_embeddings       # 图片向量（新增）
├── Elasticsearch（全文检索）
│   ├── documents             # 原始文档
│   ├── metadata              # 元数据索引（新增）
│   ├── versions              # 版本管理（新增）
│   └── audit_logs            # 审计日志（新增）
├── Redis（缓存层）【新增】
│   ├── query_cache           # 查询缓存
│   ├── embedding_cache       # 向量缓存
│   ├── session_cache         # 会话缓存
│   └── hot_documents         # 热点文档
└── MinIO（对象存储）【新增】
    ├── raw_documents         # 原始文档
    ├── processed_docs        # 处理后文档
    └── thumbnails            # 缩略图
```

### 2.4 性能优化方案

#### 2.4.1 并行化改造
```python
# 多路召回并行化
from concurrent.futures import ThreadPoolExecutor, as_completed

class ParallelRecallService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=3)
    
    def recall(self, query, embed_query, doc_list=None):
        futures = {
            self.executor.submit(self.es_recall, query): 'es',
            self.executor.submit(self.milvus_recall, embed_query, doc_list): 'milvus',
            self.executor.submit(self.kg_recall, query): 'kg'
        }
        
        results = {}
        for future in as_completed(futures):
            source = futures[future]
            try:
                results[source] = future.result()
            except Exception as e:
                logger.error(f"Recall failed for {source}: {e}")
                results[source] = []
        
        return results
```

#### 2.4.2 缓存策略
```python
# Redis缓存实现
import redis
import pickle
import hashlib

class CacheService:
    def __init__(self):
        self.redis = redis.Redis(
            connection_pool=redis.ConnectionPool(
                host=config['redis']['host'],
                port=config['redis']['port'],
                max_connections=50
            )
        )
    
    def cache_key(self, query, params):
        content = f"{query}:{str(sorted(params.items()))}"
        return f"rag:{hashlib.md5(content.encode()).hexdigest()}"
    
    def get(self, key):
        data = self.redis.get(key)
        return pickle.loads(data) if data else None
    
    def set(self, key, value, ttl=3600):
        self.redis.setex(key, ttl, pickle.dumps(value))
```

### 2.5 文档处理流程

```mermaid
graph LR
    A[文档上传] --> B[格式检测]
    B --> C{文档类型}
    C -->|PDF| D[PDFParser]
    C -->|Word| E[DocxParser]
    C -->|Excel| F[ExcelParser]
    C -->|其他| G[TikaParser]
    
    D --> H[文本提取]
    E --> H
    F --> H
    G --> H
    
    H --> I[文本预处理]
    I --> J[智能分块]
    J --> K[向量化]
    K --> L[存储]
    
    L --> M[Milvus向量]
    L --> N[ES全文索引]
    L --> O[元数据存储]
```

### 2.6 检索优化方案

#### 2.6.1 混合检索策略
```python
class HybridSearchStrategy:
    def __init__(self):
        self.strategies = {
            'dense': self.dense_search,      # 向量检索
            'sparse': self.sparse_search,     # 关键词检索
            'hybrid': self.hybrid_search,     # 混合检索
            'graph': self.graph_search        # 图谱检索
        }
    
    def search(self, query, strategy='hybrid', top_k=10):
        if strategy == 'hybrid':
            # 组合多种检索结果
            dense_results = self.dense_search(query, top_k=top_k*2)
            sparse_results = self.sparse_search(query, top_k=top_k*2)
            
            # RRF (Reciprocal Rank Fusion) 融合
            return self.rrf_fusion([dense_results, sparse_results], k=60)
        
        return self.strategies[strategy](query, top_k)
```

#### 2.6.2 智能路由
```python
class QueryRouter:
    def __init__(self):
        self.patterns = {
            'factual': r'(什么是|定义|概念|原理)',
            'procedural': r'(如何|怎么|步骤|流程)',
            'troubleshooting': r'(问题|故障|错误|异常)',
            'comparison': r'(区别|对比|相比|优劣)'
        }
    
    def route(self, query):
        query_type = self.classify_query(query)
        
        # 根据查询类型调整检索策略
        if query_type == 'factual':
            return {'strategy': 'hybrid', 'rerank_weight': 0.7}
        elif query_type == 'procedural':
            return {'strategy': 'dense', 'rerank_weight': 0.8}
        elif query_type == 'troubleshooting':
            return {'strategy': 'graph', 'rerank_weight': 0.6}
        else:
            return {'strategy': 'hybrid', 'rerank_weight': 0.5}
```

## 三、实施计划

### 第一阶段（1个月）- 基础能力建设
1. **文档解析模块开发**
   - 实现PDF、Word、Excel基础解析
   - 集成Apache Tika作为通用解析器
   - 开发文档预处理pipeline

2. **性能优化**
   - 实现多路召回并行化
   - 添加Redis缓存层
   - 优化数据库连接池

### 第二阶段（2个月）- 检索能力增强
1. **向量检索优化**
   - 本地部署BGE-M3模型
   - 实现分层向量索引
   - 优化向量相似度计算

2. **混合检索实现**
   - 开发智能查询路由
   - 实现RRF融合算法
   - 优化重排序策略

### 第三阶段（3个月）- 高级功能
1. **多模态支持**
   - 添加图片向量化能力
   - 实现图文混合检索
   - 支持表格数据提取

2. **知识管理**
   - 文档版本控制
   - 增量更新机制
   - 知识图谱增强

## 四、风险评估与应对

### 4.1 技术风险
| 风险项 | 影响程度 | 应对措施 |
|--------|----------|----------|
| 文档解析准确率 | 高 | 多解析器备份，人工审核机制 |
| 向量模型效果 | 中 | A/B测试，保留原有模型 |
| 系统性能瓶颈 | 高 | 分阶段优化，压力测试 |
| 数据安全 | 高 | 加密存储，访问控制 |

### 4.2 实施建议
1. **渐进式改造**：保持现有系统稳定运行，新功能逐步上线
2. **灰度发布**：新功能先在小范围测试，验证后全量发布
3. **监控先行**：完善监控体系，及时发现和解决问题
4. **文档完善**：同步更新技术文档和操作手册

## 五、预期效果

### 5.1 性能提升
- 查询响应时间降低 40-50%
- 并发处理能力提升 3倍
- 缓存命中率达到 60%+

### 5.2 功能增强
- 支持 20+ 文档格式
- 检索准确率提升 15-20%
- 支持图文混合检索

### 5.3 运维改善
- 自动化部署流程
- 完善的监控告警
- 详细的操作日志

## 六、总结

本方案基于现有 zte-ibo-acm-productretrieve 项目的技术栈和架构，提出了渐进式的改进方案。重点解决了文档解析、性能优化、检索增强等核心问题，同时保持了与现有系统的兼容性。通过分阶段实施，可以在保证系统稳定性的前提下，逐步提升知识库的能力和性能。