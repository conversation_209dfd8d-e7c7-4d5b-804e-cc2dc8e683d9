# 【知识检索系统】_【架构分析】_20250731162423_项目理解计划

## 项目概述
ZTE产品知识检索系统 - 基于多路召回+重排序+LLM生成的智能问答系统

## 执行计划

### 阶段一：项目整体架构分析 🔍
- [x] 分析项目入口点和主要功能
- [x] 识别核心技术栈和依赖
- [x] 理解项目目录结构
- [ ] 深入分析核心服务逻辑

### 阶段二：功能模块分析 📊
- [ ] 分析召回模块（ES、Milvus、KG三路召回）
- [ ] 分析重排序机制（BGE-M3）
- [ ] 分析LLM集成和提示词管理
- [ ] 分析实体提取和链接
- [ ] 分析查询重写功能

### 阶段三：可视化图表生成 📈
1. **系统架构图** - 展示整体系统设计
2. **数据流程图** - 展示请求处理流程
3. **模块关系图** - 展示各组件交互
4. **时序图** - 展示典型请求的处理时序
5. **部署架构图** - 展示系统部署结构

### 阶段四：总结报告 📝
- [ ] 生成项目技术特点总结
- [ ] 输出架构优势和特色
- [ ] 提供改进建议

## 预期产出
- 多个Mermaid图表清晰展示系统架构
- 简洁易懂的中文说明
- 完整的项目理解报告

## 时间安排
预计完成时间：30-45分钟
开始时间：2025-01-31 16:24:23