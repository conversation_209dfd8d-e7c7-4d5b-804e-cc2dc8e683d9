import os
import fnmatch
from domain.entity.CommonValues import CommonValues
from flask import Flask, request
from flask_cors import CORS
from utils.utils import wrapper_response
from utils.logger.logger_util import logger
from utils.tokenVerify import TokenVerify
from werkzeug.serving import W<PERSON><PERSON>R<PERSON><PERSON><PERSON>andler
from controller.zxtech_controller import zxtech_subscribe

## 在这设置配置
from domain.config.zxtech_config import config

app = Flask(__name__)


@app.before_request
def before_request():
    WSGIRequestHandler.sys_version = 'dev'
    WSGIRequestHandler.server_version = ''
    # logger.info(f'Request: {request.method} {request.url}')
    json_body = {}

    if request.method in ['POST', 'PUT', 'PATCH']:
        try:
            json_body = request.get_json()
        except Exception as e:
            logger.error("Error parsing JSON data:", e)

    header = request.headers
    headers_dict = dict(header)

    # 校验token
    exclude = config['identify']['exclude']
    verify_enable = config['identify']['enable']
    if verify_enable and not path_matches(exclude, request.path):
        intercept_path = config['identify']['intercept']
        emp_no = header.get('X-Emp-No', '')
        token = header.get('X-Auth-Value', '')
        if verify_enable and path_matches(intercept_path, request.path):
            check_verify = TokenVerify(accountid=emp_no, token=token)
            verify_result, response = check_verify.token_verify()
            if not verify_result:
                return wrapper_response('0002', 'UAC Verify Fail')
                # 记录用户输入相关参数
        query_params = request.args.to_dict()
        path_params = request.view_args
        logger.info(
            f'Query Params: {query_params}, Path Params: {path_params}, JSON Body: {json_body}, Headers: {headers_dict}')

        CommonValues.set_value('token', token)
        CommonValues.set_value('emp_no', emp_no)

# Started by AICoder, pid:sd202qdf8aa8e5b148930bbd3061b5147dd31597

def path_matches(patterns, path):
    """
    检查路径是否匹配给定的模式列表中的任何一个。

    :param patterns: 模式列表，每个模式都是一个字符串。
    :param path: 要检查的路径，可以是相对路径或绝对路径。
    :return: 如果路径匹配模式列表中的任何一个，返回True；否则返回False。
    """
    # 使用列表推导式和any函数进行优化
    return any(fnmatch.fnmatchcase(os.path.normpath(path), os.path.normpath(pattern)) for pattern in patterns)

# Ended by AICoder, pid:sd202qdf8aa8e5b148930bbd3061b5147dd31597

if __name__ == '__main__':
    CORS(app, supports_credentials=True)
    app.register_blueprint(zxtech_subscribe, url_prefix='/zte-ibo-acm-productretrieve')
    app.run(host=config['Parameters']['address'], debug=False, port=config['Parameters']['port'])

