import copy
import re

from rewrite.rewrite_model import handle_related
from utils.llm_util.llm_util import llm_flag_result
from utils.logger.log_wrapper import wrap_log_content
from utils.logger.logger_util import logger
from retrieve.Recall_data import RecallResult
from utils.judge_string_type import judge_string_type
from embedding.get_embedding import GetEmbedding
from domain.config.zxtech_config import config
from entity.catch_body import dp_entity
from retrieve.milvus_recall import RecallMilvus
from rerank.bge_m3_reranker_v2 import BGEM3RerankV2
from llm.LLMResult import post_LLM_result
from retrieve.kg_retrieve.kg_retrieve import RecallResult_kg
from domain.constants.infoGo_constants import isInfoGo, version_error_response
from domain.constants.Enums import SOURCE, Hints, RelatedResult
from utils.relate_model import is_related
from utils.llm_util.llm_param import _LLM_Params_,_Stream_Generate_Params_,_Product_Version_Params_


class _ZXTECH_(object):
    def __init__(self, config):
        self.entity = dp_entity(config)
        self.getembedding = GetEmbedding(config)
        self.es_recall = RecallResult(config)
        self.milvus_recall = RecallMilvus(config)
        self.rerank_tool = BGEM3RerankV2(config)
        self.kg_recall = RecallResult_kg(config)

    def __call__(self, query, XEmpNo, history, rewriteText, g, *args, **kwargs):
        # 预处理
        LLM_config = copy.deepcopy(config['LLM'])
        LLM_config['llm_headers']['X-Emp-No'] = XEmpNo
        time_record_dict = {}
        isMulti, query, isSelected = self.process_rewrite(g, history, query, rewriteText, time_record_dict)
        query = query.replace('_', ' ')
        # ①判断用户中英文
        query_lang = judge_string_type(query)
        embed_query = self.getembedding.post_url_m3(query)
        # ②提取实体 方法依赖邓鹏 {'-1617725463': 'ZXR10 M6000-2S4 PLUS'}
        body = self.extract_entity(query)
        # {'ZXR10 M6000-8S Plus': {'product_id': 'productZXR10 M6000-8S Plus', 'series_id': 'seriesZXR10 M6000-S'}}
        # body返回为空，此时为问知识点
        if len(body) == 0:
            es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict = self.deal_nobody(query, embed_query)
            _llm_params_nobody_ = _LLM_Params_(map_dict=map_dict, body=body, doc_res=SOURCE.WU_CN.value,
                                               es_entity=SOURCE.WU_CN.value, es_res_doc=es_res_nobody,
                                               milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                               query_lang=query_lang)
            llm_res = post_LLM_result(query=query, LLM_params=_llm_params_nobody_, LLM_config=LLM_config)
            return llm_res
        # 使用正则表达式从问题中查找所有版本号
        version_list = re.findall(r'(?<![a-zA-Z0-9])[vV]\d+(?:\.\d+)*(?![a-zA-Z])', query)
        version_list = [version.replace('v', 'V') for version in version_list]
        version_list = [version for version in version_list if version not in Hints.PRODUCT_VERSION_LIST.value]
        # 如果产品型号不匹配，但是有对应系列
        product_id = list(body.values())[0]['product_id']
        if product_id == '':
            _series_case_params_=_Product_Version_Params_(body=body,embed_query=embed_query,query_lang=query_lang,version_list=version_list)
            return self.handle_series_case(query=query, LLM_config=LLM_config,Series_Case_Params=_series_case_params_)

        # 如果产品型号匹配上了
        body_ids = {}
        pid = ""
        sid = ""
        for body_id in body.values():
            body_ids[body_id['product_id']] = body_id['series_id']
            pid = body_id['product_id']
            sid = body_id['series_id']
        # 提取出所有id
        # ③调用东方总的接口获得型号所有关联的文档
        # 判断产品是否属于infoGo范围
        infoGo_flag = isInfoGo(pid)
        # 如果有指定版本号，那么根据产品号和版本号获取对应文档
        if len(version_list) > 0:
            _product_version_params_=_Product_Version_Params_(body=body,embed_query=embed_query,pid=pid,query_lang=query_lang,version_list=version_list)
            return self.handle_product_version_case(LLM_config=LLM_config, query=query, Product_Version_Params=_product_version_params_)
        # 如果没有指定版本号则查产品下所有文档
        else:
            doc_res = self.kg_recall.body_relation_doc_pid(pid)
        # 如果根据产品号查不到文档，则走系列号查询文档
        if not doc_res:
            doc_res = self.kg_recall.body_relation_doc_sid(sid)
        # 如果 型号和系列号都关联不到文档，直接走ES
        if not doc_res:
            es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict = self.deal_product_no_doc(query,
                                                                                                          embed_query,
                                                                                                          infoGo_flag)
            _llm_params_product_no_doc_ = _LLM_Params_(map_dict=map_dict, body=str(body), doc_res=SOURCE.WU_CN.value,
                                                       es_entity=es_entity, es_res_doc=es_res_nobody,
                                                       milvus_res_doc=milvus_res, kg_res=kg_res,
                                                       rerank_res_doc=rerank_res, query_lang=query_lang)
            llm_res = post_LLM_result(LLM_config=LLM_config, query=query, LLM_params=_llm_params_product_no_doc_)
            return llm_res

        document_names = [doc["documentName"] for product in doc_res.values() for doc in product]
        # 文档去重
        list_doc_set_new = list(set(document_names))
        es_entity, es_res_doc, milvus_res, kg_res, rerank_res, map_dict = self.deal_product_doc(list_doc_set_new, query,
                                                                                                embed_query,
                                                                                                infoGo_flag)
        # query:用户问题 res:文档id列表 dict_res:id对应的文章标题和文章内容 body:实体名 doc_res:实体链接文档 ids_content：向量化重排序结果
        _llm_params_product_doc_ = _LLM_Params_(map_dict=map_dict, body=str(body), doc_res=doc_res,
                                                   es_entity=es_entity, es_res_doc=es_res_doc,
                                                   milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                                   query_lang=query_lang)
        llm_res = post_LLM_result(LLM_config=LLM_config,query=query,LLM_params=_llm_params_product_doc_)
        return llm_res

    def handle_product_version_case(self, LLM_config, query,Product_Version_Params):
        body=Product_Version_Params.body
        embed_query=Product_Version_Params.embed_query
        pid=Product_Version_Params.pid
        query_lang=Product_Version_Params.query_lang
        version_list=Product_Version_Params.version_list
        doc_res = {}
        for version in version_list:
            current_res = self.kg_recall.body_relation_doc_version(pid, version, False)
            if not current_res:
                continue
            doc_res[id] = []
            for key, value in current_res.items():
                doc_res[id].extend(value)
        # 如果该产品不存在指定版本号
        if not doc_res:
            return version_error_response(list(body.keys())[0], version_list)
        # 使用指定版本文档切片
        document_names = [doc["documentName"] for product in doc_res.values() for doc in product]
        list_doc_set_new = list(set(document_names))
        es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict = self.deal_series_doc(query,
                                                                                                  embed_query,
                                                                                                  list_doc_set_new)
        llm_flag_res = llm_flag_result(rerank_res, doc_res, query_lang, kg_res, query)
        # 如果切片中包含正确答案
        if llm_flag_res:
            _llm_params_series_doc_=_LLM_Params_(map_dict=map_dict, body=str(body), doc_res=SOURCE.WU_CN.value,
                                                   es_entity=es_entity, es_res_doc=es_res_nobody,
                                                   milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                                   query_lang=query_lang)
            llm_res = post_LLM_result(LLM_config=LLM_config, query=query,LLM_params=_llm_params_series_doc_)
            return llm_res
        # 如果指定版本切片不包含正确答案，那么走原来链路使用全集回答问题
        doc_res = self.kg_recall.body_relation_doc_pid(pid)
        document_names = [doc["documentName"] for product in doc_res.values() for doc in product]
        # 文档去重
        list_doc_set_new = list(set(document_names))
        es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict = self.deal_series_doc(query,
                                                                                                  embed_query,
                                                                                                  list_doc_set_new)
        _llm_params_series_doc_ = _LLM_Params_(map_dict=map_dict, body=str(body), doc_res=SOURCE.WU_CN.value,
                                               es_entity=es_entity, es_res_doc=es_res_nobody,
                                               milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                               query_lang=query_lang)
        llm_res = post_LLM_result(LLM_config=LLM_config, query=query,LLM_params=_llm_params_series_doc_)
        return llm_res

    def extract_entity(self, query):
        try:
            bodyname, body = self.entity(query)
        except Exception as e:
            logger.info("实体提取出错")
            body = {}
        return body

    def handle_series_case(self, query, LLM_config,Series_Case_Params):
        body = Series_Case_Params.body
        embed_query = Series_Case_Params.embed_query
        query_lang = Series_Case_Params.query_lang
        version_list = Series_Case_Params.version_list
        sid = list(body.values())[0]['series_id']

        # 如果有版本号，那么根据系列号和版本号获取对应文档
        if len(version_list) > 0:
            _documents_case_params_=_Product_Version_Params_(body=body,embed_query=embed_query,query_lang=query_lang,version_list=version_list,sid=sid)
            return self.handle_versioned_documents_case(query=query,  LLM_config=LLM_config,Versioned_Documents_Case_Param=_documents_case_params_)

        _documents_case_params_ = _Product_Version_Params_(body=body, embed_query=embed_query, query_lang=query_lang,sid=sid)
        # 否则走原流程
        return self.handle_no_versioned_documents_case(query=query, LLM_config=LLM_config, Documents_Case_Params=_documents_case_params_)

    def handle_versioned_documents_case(self, query,  LLM_config,Versioned_Documents_Case_Param):
        body=Versioned_Documents_Case_Param.body
        embed_query=Versioned_Documents_Case_Param.embed_query
        sid = Versioned_Documents_Case_Param.sid
        query_lang=Versioned_Documents_Case_Param.query_lang
        version_list=Versioned_Documents_Case_Param.version_list
        doc_res = {}

        for version in version_list:
            current_res = self.kg_recall.body_relation_doc_version(sid, version, True)
            if not current_res:
                continue
            doc_res[id] = [doc for key, value in current_res.items() for doc in value]

        # 如果该系列不存在指定版本号
        if not doc_res:
            return version_error_response(list(body.keys())[0], version_list)

        # 使用指定版本文档切片
        document_names = [doc["documentName"] for product in doc_res.values() for doc in product]
        list_doc_set_new = list(set(document_names))

        es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict = self.deal_series_doc(query, embed_query,
                                                                                                  list_doc_set_new)
        llm_flag_res = llm_flag_result(rerank_res, doc_res, query_lang, kg_res, query)

        # 如果切片中包含正确答案
        if llm_flag_res:
            _llm_params_flag_=_LLM_Params_(map_dict=map_dict, body=str(body), doc_res=SOURCE.WU_CN.value,
                                               es_entity=es_entity, es_res_doc=es_res_nobody,
                                               milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                               query_lang=query_lang)
            return post_LLM_result(LLM_config=LLM_config, query=query,LLM_params=_llm_params_flag_)

        # 如果不包含正确答案，那么使用全集回答问题
        _no_version_params_=_Product_Version_Params_(body=body,embed_query=embed_query,query_lang=query_lang,sid=sid)
        return self.handle_no_versioned_documents_case(query=query, LLM_config=LLM_config,Documents_Case_Params=_no_version_params_)

    def handle_no_versioned_documents_case(self, query, LLM_config, Documents_Case_Params):
        body=Documents_Case_Params.body
        embed_query=Documents_Case_Params.embed_query
        sid = Documents_Case_Params.sid
        query_lang=Documents_Case_Params.query_lang
        doc_res = self.kg_recall.body_relation_doc_sid(sid)

        # 如果 型号和系列号都关联不到文档，直接走ES
        if not doc_res:
            es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict = self.deal_series_no_doc(query,
                                                                                                         embed_query)
            _llm_global_params_ = _LLM_Params_(map_dict=map_dict, body=str(body), doc_res=SOURCE.WU_CN.value,
                                             es_entity=es_entity, es_res_doc=es_res_nobody,
                                             milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                             query_lang=query_lang)
            return post_LLM_result(LLM_config=LLM_config, query=query,LLM_params=_llm_global_params_)

        document_names = [doc["documentName"] for product in doc_res.values() for doc in product]
        list_doc_set_new = list(set(document_names))

        es_entity, es_res_doc, milvus_res, kg_res, rerank_res, map_dict = self.deal_series_doc(query, embed_query,
                                                                                               list_doc_set_new)
        _llm_doc_params_ = _LLM_Params_(map_dict=map_dict, body=str(body), doc_res=doc_res,
                                           es_entity=es_entity, es_res_doc=es_res_doc,
                                           milvus_res_doc=milvus_res, kg_res=kg_res, rerank_res_doc=rerank_res,
                                           query_lang=query_lang)
        return post_LLM_result(LLM_config=LLM_config, query=query, LLM_params=_llm_doc_params_)

    def deal_nobody(self, query, embed_query):
        es_res_nobody = self.es_recall.query_data_es(query)
        milvus_res = self.milvus_recall.query_data_milvus(embed_query, None)
        # 增加第三路kg召回
        kg_res, es_entity, map_dict = self.es_recall.get_kg_result_with_full_es_link(query, True)
        rerank_res = self.rerank_tool.rerankV2(query, milvus_res + es_res_nobody)

        # es去重
        seen_content = set()
        result_single = []
        for item in rerank_res:
            if item['content'] not in seen_content:
                result_single.append(item)
                seen_content.add(item['content'])
        rerank_res = result_single

        id_to_doc_map = {}
        for doc in rerank_res:
            # 将文档的id作为键，文档名称作为值，添加到映射字典中
            id_to_doc_map[doc["id"]] = doc["doc_name"]
        return es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict

    def deal_series_no_doc(self, query, embed_query):
        es_res_nobody = self.es_recall.query_data_es(query)
        milvus_res = self.milvus_recall.query_data_milvus(embed_query, None)
        # 增加第三路kg召回
        kg_res, es_entity, map_dict = self.es_recall.get_kg_result_with_full_es_link(query)
        rerank_res = self.rerank_tool.rerankV2(query, milvus_res + es_res_nobody)

        seen_content = set()
        result_single = []
        for item in rerank_res:
            if item['content'] not in seen_content:
                result_single.append(item)
                seen_content.add(item['content'])
        rerank_res = result_single

        id_to_doc_map = {}
        for doc in rerank_res:
            # 将文档的id作为键，文档名称作为值，添加到映射字典中
            id_to_doc_map[doc["id"]] = doc["doc_name"]
        return es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict

    def deal_series_doc(self, query, embed_query, list_doc_set_new):
        # ④用用户问题去doc_set里的content模糊匹配 -top10(精确+模糊)
        es_res_doc = self.es_recall.query_data_fuzzyandprecise(list_doc_set_new, query)
        milvus_res = self.milvus_recall.query_data_milvus(embed_query, list_doc_set_new)
        # 增加第三路kg召回
        kg_res, es_entity, map_dict = self.es_recall.get_kg_result_with_full_es_link(query)
        rerank_res = self.rerank_tool.rerankV2(query, milvus_res + es_res_doc)

        seen_content = set()
        result_single = []
        for item in rerank_res:
            if item['content'] not in seen_content:
                result_single.append(item)
                seen_content.add(item['content'])
        rerank_res = result_single

        id_to_doc_map = {}

        # 遍历A中的每个文档集合
        for doc in rerank_res:
            # 将文档的id作为键，文档名称作为值，添加到映射字典中
            id_to_doc_map[doc["id"]] = doc["doc_name"]
        return es_entity, es_res_doc, milvus_res, kg_res, rerank_res, map_dict

    def deal_product_no_doc(self, query, embed_query, infoGo_flag):
        es_res_nobody = self.es_recall.query_data_es(query)
        milvus_res = self.milvus_recall.query_data_milvus(embed_query, None)
        # 增加第三路kg召回
        kg_res, es_entity, map_dict = self.es_recall.get_kg_result_with_full_es_link(query, infoGo_flag)
        rerank_res = self.rerank_tool.rerankV2(query, milvus_res + es_res_nobody)

        seen_content = set()
        result_single = []
        for item in rerank_res:
            if item['content'] not in seen_content:
                result_single.append(item)
                seen_content.add(item['content'])
        rerank_res = result_single

        id_to_doc_map = {}
        for doc in rerank_res:
            # 将文档的id作为键，文档名称作为值，添加到映射字典中
            id_to_doc_map[doc["id"]] = doc["doc_name"]
        return es_entity, es_res_nobody, milvus_res, kg_res, rerank_res, map_dict

    def deal_product_doc(self, list_doc_set_new, query, embed_query, infoGo_flag):
        # ④用用户问题去doc_set里的content模糊匹配 -top10(精确+模糊)
        es_res_doc = self.es_recall.query_data_fuzzyandprecise(list_doc_set_new, query)
        milvus_res = self.milvus_recall.query_data_milvus(embed_query, list_doc_set_new)
        # 增加第三路kg召回
        kg_res, es_entity, map_dict = self.es_recall.get_kg_result_with_full_es_link(query, infoGo_flag)
        rerank_res = self.rerank_tool.rerankV2(query, milvus_res + es_res_doc)

        seen_content = set()
        result_single = []
        for item in rerank_res:
            if item['content'] not in seen_content:
                result_single.append(item)
                seen_content.add(item['content'])
        rerank_res = result_single

        id_to_doc_map = {}

        # 遍历A中的每个文档集合
        for doc in rerank_res:
            # 将文档的id作为键，文档名称作为值，添加到映射字典中
            id_to_doc_map[doc["id"]] = doc["doc_name"]
        return es_entity, es_res_doc, milvus_res, kg_res, rerank_res, map_dict

    def process_rewrite(self, g, history, origin_text, rewriteText, time_record_dict):
        # 初始化文本和是否为多轮对话的标志
        rewrite_question = origin_text
        isSelected = rewriteText is None
        isMulti = False

        try:
            # 如果history存在且长度大于等于0，则获取最后一个用户发言的内容
            if history and len(history) >= 1:
            # Started by AICoder, pid:tf66dk56de6c37a147bf0b0bb0c55715fc52df96
                # 检查是否被选中
                if isSelected:
                    # 如果被选中，则调用handle_related函数重写
                    rewrite_question = handle_related(origin_text, history, time_record_dict, g=g)
                    isMulti = True
                else:
                    # 如果没有被选中，则直接从rewriteText字典中获取continue键的值，并将其与RelatedResult.MULTI的值进行比较
                    isMulti = rewriteText == RelatedResult.MULTI.value
                    rewrite_question = rewriteText.get('rewrite')

        except Exception as e:
            # 记录错误日志
            logger.error(wrap_log_content(f"history解析/重写报错:{e},history为:{history}", g))

        # 返回是否为多轮对话和处理后的文本
        return isMulti, rewrite_question, isSelected
